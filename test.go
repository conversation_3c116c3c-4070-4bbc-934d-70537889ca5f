package main

import (
	"bufio"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"io"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"
)

// 定义常量
const (
	websocketURI   = "wss://matrix.tencent.com/ai_gen_txt_server/getClassify"
	browserCookies = `sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22196f394978c53c-09af953edbd4748-26011f51-1327104-196f394978d10ae%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk2ZjM5NDk3OGM1M2MtMDlhZjk1M2VkYmQ0NzQ4LTI2MDExZjUxLTEzMjcxMDQtMTk2ZjM5NDk3OGQxMGFlIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22196f394978c53c-09af953edbd4748-26011f51-1327104-196f394978d10ae%22%7D; _gcl_au=1.1.750546091.1747843324; _ga_RPMZTEBERQ=GS2.1.s1747889012$o1$g1$t1747889512$j0$l0$h0; _ga=GA1.2.567199319.1747843324; qcloud_from=qcloud.bing.seo-1752807773858; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E5%85%B6%E4%BB%96-%E5%BC%80%E6%BA%90%2C%E5%85%B6%E4%BB%96-%E7%A9%BA%E7%B1%BB-%E5%B7%A5%E5%85%B7%2C%E5%85%B6%E4%BB%96-%E7%A9%BA%E7%B1%BB-%E7%9B%91%E6%8E%A7%2C%E5%85%B6`
	captchaTicket  = `t03I-LEsLWnsGBT3OKL4Q0bOjGfwOc-cB9vRbE07iWvRIPmzYZuuaXVOi_h-L6QFNaxcZnEBnp9sem3g6tMcx1ah_VUKxC1EhnalM9rMvUxuyiYxNjy27SoIcEQZmWPe0PNiqaS3mW3TVQ4uhtdpKectW8FkJqcOtwYTsYVZBD6kuPsjPRfzxB77Q**`
)

var yourText = `在中国"嫦娥一号"探月卫星于明年发射之后,中国还将发射三颗"夸父"卫星"逐日"。昨天,在第36届世界空间科技大会期间，中国宇航局发言人正式对外宣布了这一宏伟计划。这一计划的实施，不仅标志着中国航天事业在深空探测领域取得了新的重大突破，也展示了中国政府对于未来航天发展的宏伟蓝图和坚定信念。
"夸父"卫星项目，作为中国古代神话中追逐太阳的英雄的名字，寓意着探索太阳的无限可能。这一项目计划在未来几年内，通过发射三颗卫星，实现对太阳的全面、深入观测。这些卫星将携带先进的观测仪器，对太阳进行全方位、多角度的观测，获取大量关于太阳的宝贵数据。这些数据不仅有助于科学家们更深入地理解太阳的物理特性，为未来的空间天气预报提供更为精准的数据支持，还将推动相关领域的科学研究和技术创新。
第一颗"夸父"卫星，计划在未来两年内发射。这颗卫星的主要任务是进行太阳风的综合观测。通过搭载的高精度仪器，它将实时监测太阳风的速度、方向、温度等关键参数，为科学家们提供太阳风的详尽数据。这些数据将有助于科学家们更好地理解太阳风对地球空间环境的影响，预测太阳活动对地球空间环境的潜在威胁，为航天器的安全运行提供更为可靠的保障。
第二颗"夸父"卫星，将在第一颗卫星发射后的两年内发射。这颗卫星的主要目标是观测太阳耀斑和日冕物质抛射现象。通过搭载的先进观测设备，它将捕捉到太阳耀斑和日冕物质抛射的清晰图像和数据，揭示这些现象背后的物理机制。这些数据将有助于科学家们更好地预测太阳活动，降低太阳活动对地球空间环境和人类活动的影响，为地球空间环境的稳定和安全提供有力支持。
第三颗"夸父"卫星，则计划在第二颗卫星发射后的两年内发射。这颗卫星的主要任务是进行太阳和地球空间环境的联合观测。通过搭载的多波段观测仪器，它将同时观测太阳和地球空间环境的变化，揭示它们之间的相互作用和影响机制。这些数据将有助于科学家们更好地理解太阳活动对地球空间环境的影响，为航天器的运行和人类活动提供更为准确的空间天气预报。
"夸父"卫星项目的实施，无疑将为中国的航天事业注入新的活力。通过这一项目，中国不仅将进一步提升在深空探测领域的技术水平，还将为全球空间科学的发展做出重要贡献。同时，"夸父"卫星项目也将激发更多年轻人对航天科技的兴趣和热情，为中国航天事业的持续发展培养更多的优秀人才。`

func main() {
	// 配置 TLS
	tlsConfig := &tls.Config{
		RootCAs: nil,
	}

	// 设置 WebSocket 拨号器，添加完整的请求头
	dialer := websocket.Dialer{
		TLSClientConfig: tlsConfig,
	}

	// 设置完整的请求头
	headers := http.Header{
		"Origin":          []string{"https://matrix.tencent.com"},
		"Cache-Control":   []string{"no-cache"},
		"Accept-Language": []string{"zh-CN,zh;q=0.9"},
		"Pragma":          []string{"no-cache"},
		"User-Agent":      []string{"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"},
		"Cookie":          []string{browserCookies},
	}

	// 建立 WebSocket 连接
	conn, _, err := dialer.Dial(websocketURI, headers)
	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}
	defer conn.Close()
	fmt.Println("✅ WebSocket 连接已成功建立")

	// 1. 发送指纹信息
	fpPayload, _ := json.Marshal(map[string]string{"fp": "a2b4126d8a97995ecb07205636889192"})
	err = conn.WriteMessage(websocket.TextMessage, fpPayload)
	if err != nil {
		fmt.Printf("❌ 发送指纹失败: %v\n", err)
		return
	}
	fmt.Printf("  ⬆️ 已发送指纹: %s\n", fpPayload)

	// 接收状态响应
	_, message, err := conn.ReadMessage()
	if err != nil {
		fmt.Printf("❌ 读取状态失败: %v\n", err)
		return
	}
	fmt.Printf("  ⬇️ 收到状态: %s\n", message)

	// 2. 发送人机验证凭证
	ticketPayload, _ := json.Marshal(map[string]string{
		"ticket":  captchaTicket,
		"randstr": "@HyN",
	})
	err = conn.WriteMessage(websocket.TextMessage, ticketPayload)
	if err != nil {
		fmt.Printf("❌ 发送凭证失败: %v\n", err)
		return
	}
	fmt.Printf("  ⬆️ 已发送凭证: %s\n", ticketPayload)

	// 接收验证结果
	_, message, err = conn.ReadMessage()
	if err != nil {
		fmt.Printf("❌ 读取验证结果失败: %v\n", err)
		return
	}
	fmt.Printf("  ⬇️ 收到验证结果: %s\n", message)

	// 3. 发送文本内容
	textPayload, _ := json.Marshal(map[string]string{"text": yourText})
	err = conn.WriteMessage(websocket.TextMessage, textPayload)
	if err != nil {
		fmt.Printf("❌ 发送文本失败: %v\n", err)
		return
	}
	fmt.Printf("  ⬆️ 已发送文本内容\n")

	fmt.Println("\n✅ 数据已全部发送，正在等待服务器处理结果...")

	// 循环接收服务器返回的消息
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
				fmt.Printf("❌ 连接被关闭: %v\n", err)
			} else {
				fmt.Printf("❌ 读取消息失败: %v\n", err)
			}
			break
		}
		
		fmt.Printf("  ⬇️ 收到消息: %s\n", message)
		
		// 解析消息判断是否为最终结果
		var result map[string]interface{}
		if json.Unmarshal(message, &result) == nil {
			if status, ok := result["status"].(string); ok && status == "success" {
				if _, hasLabels := result["labels_ratio"]; hasLabels {
					fmt.Println("\n🎉 分析完成！")
					break
				}
			}
		}
	}
}
